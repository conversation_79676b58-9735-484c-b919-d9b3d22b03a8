<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户系统测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 4px; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Seeq用户系统测试</h1>
    
    <div class="test-section">
        <h2>1. 用户注册测试</h2>
        <input type="text" id="registerQQ" placeholder="QQ号" value="1234567890">
        <button onclick="testRegister()">测试注册</button>
        <div id="registerResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 用户登录测试</h2>
        <input type="text" id="loginCode" placeholder="登录码">
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 获取用户信息测试</h2>
        <input type="text" id="userID" placeholder="16位用户ID" value="1234567890123456">
        <button onclick="testGetUser()">获取用户信息</button>
        <div id="userResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 获取EQ列表测试</h2>
        <button onclick="testGetEQs()">获取EQ列表</button>
        <div id="eqResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>5. 测试用户名动态更新</h2>
        <p>这个测试展示了EQ作者信息如何通过用户ID动态关联</p>
        <input type="text" id="newUsername" placeholder="新用户名" value="动态更新测试">
        <button onclick="testUpdateUsername()">更新用户名</button>
        <button onclick="testGetEQAfterUpdate()">获取EQ查看更新效果</button>
        <div id="updateResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        
        async function testRegister() {
            const qq = document.getElementById('registerQQ').value;
            const resultDiv = document.getElementById('registerResult');
            
            try {
                const response = await fetch(`${API_BASE}/api/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        token: 'Seeq233UserManege',
                        qq: qq
                    })
                });
                
                const result = await response.json();
                resultDiv.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                
                if (result.success) {
                    document.getElementById('loginCode').value = result.loginCode;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">错误: ${error.message}</span>`;
            }
        }
        
        async function testLogin() {
            const loginCode = document.getElementById('loginCode').value;
            const resultDiv = document.getElementById('loginResult');
            
            try {
                const response = await fetch(`${API_BASE}/api/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        token: 'SeeqYKload233LOGIN',
                        loginCode: loginCode
                    })
                });
                
                const result = await response.json();
                resultDiv.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                
                if (result.success) {
                    document.getElementById('userID').value = result.userID;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">错误: ${error.message}</span>`;
            }
        }
        
        async function testGetUser() {
            const userID = document.getElementById('userID').value;
            const resultDiv = document.getElementById('userResult');
            
            try {
                const response = await fetch(`${API_BASE}/api/user/current`, {
                    headers: {
                        'User-Agent': `SeeqApp/1.0 (${userID})`
                    }
                });
                
                const result = await response.json();
                resultDiv.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">错误: ${error.message}</span>`;
            }
        }
        
        async function testGetEQs() {
            const resultDiv = document.getElementById('eqResult');
            
            try {
                const response = await fetch(`${API_BASE}/api/eq`);
                const result = await response.json();
                
                if (result.success && result.data.length > 0) {
                    const eq = result.data[0]; // 显示第一个EQ
                    resultDiv.innerHTML = `
                        <h4>第一个EQ信息:</h4>
                        <div style="display: flex; align-items: center; margin: 10px 0;">
                            <img src="${eq.uploaderAvatar || 'https://q1.qlogo.cn/g?b=qq&nk=10000&s=640'}" 
                                 alt="${eq.author}" 
                                 style="width: 32px; height: 32px; border-radius: 50%; margin-right: 10px;"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiNEMUQ1REIiLz4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxMiIgcj0iNSIgZmlsbD0iIzZCNzI4MCIvPgo8cGF0aCBkPSJNNiAyNmMwLTUgNC41LTkgMTAtOXMxMCA0IDEwIDkiIGZpbGw9IiM2QjcyODAiLz4KPC9zdmc+Cg=='">
                            <div>
                                <strong>${eq.name}</strong><br>
                                <small>by ${eq.author} | ${eq.category}</small>
                            </div>
                        </div>
                        <pre>${JSON.stringify(eq, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">错误: ${error.message}</span>`;
            }
        }

        async function testUpdateUsername() {
            const newUsername = document.getElementById('newUsername').value;
            const userID = document.getElementById('userID').value;
            const resultDiv = document.getElementById('updateResult');

            try {
                const response = await fetch(`${API_BASE}/api/user/set-username`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': `SeeqApp/1.0 (${userID})`
                    },
                    body: JSON.stringify({ username: newUsername })
                });

                const result = await response.json();
                resultDiv.innerHTML = `<h4>用户名更新结果:</h4><pre>${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">错误: ${error.message}</span>`;
            }
        }

        async function testGetEQAfterUpdate() {
            const resultDiv = document.getElementById('updateResult');

            try {
                const response = await fetch(`${API_BASE}/api/eq/test-eq-with-user`);
                const result = await response.json();

                if (result.success) {
                    const eq = result.data;
                    resultDiv.innerHTML += `
                        <h4>EQ作者信息（动态更新后）:</h4>
                        <div style="display: flex; align-items: center; margin: 10px 0; padding: 10px; background: #e8f5e8; border-radius: 4px;">
                            <img src="${eq.uploaderAvatar || 'https://q1.qlogo.cn/g?b=qq&nk=10000&s=640'}"
                                 alt="${eq.author}"
                                 style="width: 32px; height: 32px; border-radius: 50%; margin-right: 10px;"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiNEMUQ1REIiLz4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxMiIgcj0iNSIgZmlsbD0iIzZCNzI4MCIvPgo8cGF0aCBkPSJNNiAyNmMwLTUgNC41LTkgMTAtOXMxMCA0IDEwIDkiIGZpbGw9IiM2QjcyODAiLz4KPC9zdmc+Cg=='">
                            <div>
                                <strong>${eq.name}</strong><br>
                                <small>作者: ${eq.author} (用户ID: ${eq.uploaderID})</small><br>
                                <small style="color: green;">✓ 作者信息已通过用户ID动态更新</small>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<span style="color: red;">错误: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
