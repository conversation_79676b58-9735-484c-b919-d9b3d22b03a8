// Seeq 前端应用主文件
class SeeqApp {
    constructor() {
        this.currentUser = null;
        this.userID = null;
        this.eqs = [];
        this.currentCategory = localStorage.getItem('seeq_category') || 'all';
        this.currentSort = localStorage.getItem('seeq_sort') || 'views';

        this.init();
    }

    async init() {
        await this.checkUserAuth();
        this.setupEventListeners();
        this.loadEQs();
        this.setupFilters();
        this.animatePageLoad();
    }

    // 检查用户身份验证和用户名设置
    async checkUserAuth() {
        try {
            const response = await fetch('/api/user/current');
            const result = await response.json();

            if (result.success) {
                this.currentUser = result.data;
                this.userID = result.data.userID;

                // 检查用户名是否为null
                if (this.currentUser.username === null) {
                    this.showUsernameSetupModal();
                }
            } else {
                console.error('用户身份验证失败:', result.error);
                // 如果身份验证失败，显示错误信息
                this.showAuthError();
            }
        } catch (error) {
            console.error('检查用户身份验证错误:', error);
            this.showAuthError();
        }
    }

    // 显示身份验证错误
    showAuthError() {
        document.body.innerHTML = `
            <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
                <div class="bg-white rounded-2xl p-8 shadow-xl max-w-md w-full mx-4">
                    <div class="text-center">
                        <div class="text-6xl mb-4">🔒</div>
                        <h1 class="text-2xl font-bold text-gray-800 mb-4">访问受限</h1>
                        <p class="text-gray-600 mb-6">
                            您需要通过官方APP登录才能访问Seeq网站。<br>
                            请使用APP扫码或输入登录码进行登录。
                        </p>
                        <div class="text-sm text-gray-500">
                            如果您已经登录，请检查您的网络连接或联系管理员。
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 显示用户名设置模态框
    showUsernameSetupModal() {
        const modal = document.createElement('div');
        modal.id = 'usernameSetupModal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-2xl p-8 shadow-xl max-w-md w-full mx-4">
                <div class="text-center mb-6">
                    <div class="text-4xl mb-4">👋</div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">欢迎来到Seeq！</h2>
                    <p class="text-gray-600">请设置您的用户名以开始使用</p>
                </div>

                <form id="usernameSetupForm" class="space-y-4">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                            用户名
                        </label>
                        <input
                            type="text"
                            id="username"
                            name="username"
                            maxlength="12"
                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="请输入用户名（最多12个字符）"
                            required
                        >
                        <div class="text-xs text-gray-500 mt-1">
                            <span id="charCount">0</span>/12 字符
                        </div>
                    </div>

                    <button
                        type="submit"
                        id="setUsernameBtn"
                        class="w-full gradient-bg text-white py-3 rounded-xl font-medium hover:opacity-90 transition-opacity"
                    >
                        <span id="setUsernameText">设置用户名</span>
                        <div id="setUsernameSpinner" class="hidden inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
                    </button>
                </form>
            </div>
        `;

        document.body.appendChild(modal);

        // 设置事件监听器
        const form = document.getElementById('usernameSetupForm');
        const usernameInput = document.getElementById('username');
        const charCount = document.getElementById('charCount');

        // 字符计数
        usernameInput.addEventListener('input', () => {
            charCount.textContent = usernameInput.value.length;
        });

        // 表单提交
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.setUsername(usernameInput.value.trim());
        });

        // 聚焦到输入框
        setTimeout(() => usernameInput.focus(), 100);
    }

    // 设置用户名
    async setUsername(username) {
        const btn = document.getElementById('setUsernameBtn');
        const btnText = document.getElementById('setUsernameText');
        const spinner = document.getElementById('setUsernameSpinner');

        // 显示加载状态
        btn.disabled = true;
        btnText.classList.add('hidden');
        spinner.classList.remove('hidden');

        try {
            const response = await fetch('/api/user/set-username', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username })
            });

            const result = await response.json();

            if (result.success) {
                this.currentUser = result.data;

                // 移除模态框
                const modal = document.getElementById('usernameSetupModal');
                if (modal) {
                    modal.remove();
                }

                // 显示欢迎消息
                this.showWelcomeMessage(username);
            } else {
                alert('设置用户名失败: ' + result.error);
                this.resetUsernameButton();
            }
        } catch (error) {
            console.error('设置用户名错误:', error);
            alert('设置用户名失败，请稍后重试');
            this.resetUsernameButton();
        }
    }

    // 重置用户名设置按钮
    resetUsernameButton() {
        const btn = document.getElementById('setUsernameBtn');
        const btnText = document.getElementById('setUsernameText');
        const spinner = document.getElementById('setUsernameSpinner');

        btn.disabled = false;
        btnText.classList.remove('hidden');
        spinner.classList.add('hidden');
    }

    // 显示欢迎消息
    showWelcomeMessage(username) {
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-xl shadow-lg z-50';
        toast.innerHTML = `
            <div class="flex items-center space-x-2">
                <span>🎉</span>
                <span>欢迎，${username}！</span>
            </div>
        `;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 筛选和排序
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.currentCategory = e.target.value;
            localStorage.setItem('seeq_category', this.currentCategory);
            this.loadEQs();
        });
        
        document.getElementById('sortFilter').addEventListener('change', (e) => {
            this.currentSort = e.target.value;
            localStorage.setItem('seeq_sort', this.currentSort);
            this.loadEQs();
        });
        
        // 上传按钮
        document.getElementById('uploadBtn').addEventListener('click', () => {
            this.showUploadModal();
        });
        
        // 模态框关闭
        document.getElementById('closeUploadModal').addEventListener('click', () => {
            this.hideUploadModal();
        });
        
        // 上传表单
        document.getElementById('uploadForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.uploadEQImage();
        });

        // 图片上传处理
        this.setupImageUpload();
        
        // 点击模态框外部关闭
        document.getElementById('eqModal').addEventListener('click', (e) => {
            if (e.target.id === 'eqModal') {
                this.hideEQModal();
            }
        });
        
        document.getElementById('uploadModal').addEventListener('click', (e) => {
            if (e.target.id === 'uploadModal') {
                this.hideUploadModal();
            }
        });
    }
    
    // 设置筛选器初始值
    setupFilters() {
        document.getElementById('categoryFilter').value = this.currentCategory;
        document.getElementById('sortFilter').value = this.currentSort;
    }

    // 设置图片上传功能
    setupImageUpload() {
        const imageUploadArea = document.getElementById('imageUploadArea');
        const imageInput = document.getElementById('eqImage');
        const uploadPlaceholder = document.getElementById('uploadPlaceholder');
        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');

        // 点击上传区域
        imageUploadArea.addEventListener('click', () => {
            imageInput.click();
        });

        // 拖拽上传
        imageUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            imageUploadArea.classList.add('border-blue-400', 'bg-blue-50');
        });

        imageUploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            imageUploadArea.classList.remove('border-blue-400', 'bg-blue-50');
        });

        imageUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            imageUploadArea.classList.remove('border-blue-400', 'bg-blue-50');

            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                imageInput.files = files;
                this.handleImageSelect(files[0]);
            }
        });

        // 文件选择
        imageInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleImageSelect(e.target.files[0]);
            }
        });
    }

    // 处理图片选择
    handleImageSelect(file) {
        const uploadPlaceholder = document.getElementById('uploadPlaceholder');
        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');

        // 显示图片预览
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            uploadPlaceholder.classList.add('hidden');
            imagePreview.classList.remove('hidden');

            // 启用提交按钮
            submitBtn.disabled = false;
            submitText.textContent = '上传并分析EQ设置';
        };
        reader.readAsDataURL(file);
    }
    

    
    // 加载EQ数据
    async loadEQs() {
        try {
            this.showLoading();
            
            const response = await fetch(`/api/eq?category=${this.currentCategory}&sort=${this.currentSort}`);
            const result = await response.json();
            
            if (result.success) {
                this.eqs = result.data;
                this.renderEQs();
            } else {
                console.error('加载EQ失败:', result.error);
                this.showEmptyState();
            }
        } catch (error) {
            console.error('网络错误:', error);
            this.showEmptyState();
        }
    }
    
    // 渲染EQ卡片
    renderEQs() {
        const grid = document.getElementById('eqGrid');
        const loading = document.getElementById('loading');
        const emptyState = document.getElementById('emptyState');
        
        loading.classList.add('hidden');
        
        if (this.eqs.length === 0) {
            grid.classList.add('hidden');
            emptyState.classList.remove('hidden');
            return;
        }
        
        emptyState.classList.add('hidden');
        grid.classList.remove('hidden');
        
        grid.innerHTML = '';
        
        this.eqs.forEach((eq, index) => {
            const card = this.createEQCard(eq);
            grid.appendChild(card);
            
            // 添加进入动画
            gsap.fromTo(card, 
                { opacity: 0, y: 50 },
                { opacity: 1, y: 0, duration: 0.6, delay: index * 0.1 }
            );
        });
    }
    
    // 创建EQ卡片
    createEQCard(eq) {
        const card = document.createElement('div');
        card.className = 'bg-white rounded-2xl p-6 shadow-lg card-hover cursor-pointer';

        // 获取用户头像，如果没有则使用默认头像
        const avatar = eq.uploaderAvatar || `https://q1.qlogo.cn/g?b=qq&nk=${eq.uploaderID || '10000'}&s=640`;
        const author = eq.author || '匿名用户';

        card.innerHTML = `
            <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">${eq.name}</h3>
                    <p class="text-gray-600 text-sm line-clamp-2">${eq.description}</p>
                </div>
                <div class="ml-4 text-right">
                    <div class="text-xs text-gray-500 mb-1">${eq.category}</div>
                    <div class="flex items-center justify-end text-xs text-gray-500">
                        <img src="${avatar}" alt="${author}" class="w-4 h-4 rounded-full mr-1" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjgiIGZpbGw9IiNEMUQ1REIiLz4KPGNpcmNsZSBjeD0iOCIgY3k9IjYiIHI9IjIuNSIgZmlsbD0iIzZCNzI4MCIvPgo8cGF0aCBkPSJNMyAxM2MwLTIuNSAyLjI1LTQuNSA1LTQuNXM1IDIgNSA0LjUiIGZpbGw9IiM2QjcyODAiLz4KPC9zdmc+Cg=='">
                        <span>by ${author}</span>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between text-sm text-gray-500">
                <span>👁️ ${eq.views || 0}</span>
                <span>❤️ ${eq.likes || 0}</span>
                <span>😊 ${eq.happiness || 0}%</span>
            </div>
        `;
        
        card.addEventListener('click', () => {
            this.showEQDetails(eq.id);
        });
        
        return card;
    }
    

    
    // 显示EQ详情
    async showEQDetails(eqId) {
        try {
            const response = await fetch(`/api/eq/${eqId}`);
            const result = await response.json();
            
            if (result.success) {
                const eq = result.data;
                this.renderEQModal(eq);
                this.showEQModal();
            }
        } catch (error) {
            console.error('获取EQ详情失败:', error);
        }
    }
    
    // 渲染EQ模态框
    renderEQModal(eq) {
        const modalContent = document.getElementById('modalContent');
        const canLike = !eq.likedBy || !eq.likedBy.includes(this.userID);
        
        modalContent.innerHTML = `
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-gray-800">${eq.name}</h3>
                    <button onclick="seeqApp.hideEQModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="mb-6">
                    <p class="text-gray-600 mb-4">${eq.description}</p>
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <div class="flex items-center">
                            <img src="${eq.uploaderAvatar || `https://q1.qlogo.cn/g?b=qq&nk=${eq.uploaderID || '10000'}&s=640`}"
                                 alt="${eq.author || '匿名用户'}"
                                 class="w-5 h-5 rounded-full mr-2"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iMTAiIGZpbGw9IiNEMUQ1REIiLz4KPGNpcmNsZSBjeD0iMTAiIGN5PSI3LjUiIHI9IjMiIGZpbGw9IiM2QjcyODAiLz4KPHBhdGggZD0iTTQgMTZjMC0zIDIuNS01IDYtNXM2IDIgNiA1IiBmaWxsPSIjNkI3MjgwIi8+Cjwvc3ZnPgo='">
                            <span>作者: ${eq.author || '匿名用户'}</span>
                        </div>
                        <span>${eq.category}</span>
                    </div>
                </div>
                
                <div class="mb-6">
                    <div class="bg-gray-50 rounded-xl p-4">
                        ${this.renderEQChart(eq.frequencies)}
                    </div>
                </div>
                
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span>👁️ ${eq.views || 0} 浏览</span>
                        <span>❤️ ${eq.likes || 0} 点赞</span>
                        <span>😊 ${eq.happiness || 0}% 欢率</span>
                    </div>
                </div>
                
                <div class="flex space-x-3">
                    <button onclick="seeqApp.importToHuanlu('${eq.id}')"
                            class="flex-1 gradient-bg text-white py-3 rounded-xl font-medium hover:opacity-90 transition-opacity">
                        一键导入欢律
                    </button>
                    <button ${canLike ? '' : 'disabled'}
                            onclick="seeqApp.likeEQ('${eq.id}')"
                            class="px-6 py-3 ${canLike ? 'bg-white border border-gray-300 hover:bg-gray-50' : 'bg-gray-100 border border-gray-200'} rounded-xl font-medium transition-colors ${canLike ? '' : 'cursor-not-allowed'}">
                        ${canLike ? '❤️ 点赞' : '✅ 已点赞'}
                    </button>
                </div>
            </div>
        `;
    }
    
    // 渲染EQ图表（详情页面）
    renderEQChart(frequencies) {
        const freqs = ['62', '250', '1k', '4k', '8k', '16k'];
        const values = freqs.map(freq => frequencies[freq] || 0);

        // 计算控制点位置
        const chartWidth = 300; // SVG图表宽度
        const chartHeight = 180; // SVG图表高度
        const leftPadding = 50; // 左侧padding
        const rightPadding = 30; // 右侧padding（增加以防止控制点被截断）
        const topPadding = 30; // 顶部padding
        const controlPointRadius = 5; // 控制点半径
        const controlPointMargin = 10; // 控制点边距

        // SVG内部的有效绘图区域（减去控制点边距）
        const drawingWidth = chartWidth - (controlPointMargin * 2);
        const drawingStartX = controlPointMargin;

        // 创建SVG图表
        let svg = `
            <div class="relative" style="height: 280px; width: 100%;">
                <!-- 顶部数值显示 -->
                <div class="absolute top-0 flex text-sm font-medium text-blue-500" style="left: ${leftPadding}px; right: ${rightPadding}px;">
                    ${values.map((value, index) => {
                        const controlPointX = drawingStartX + (index * (drawingWidth / (values.length - 1)));
                        const leftPos = (controlPointX / chartWidth) * 100;
                        return `<span style="position: absolute; left: ${leftPos}%; transform: translateX(-50%);">${value > 0 ? '+' : ''}${value}</span>`;
                    }).join('')}
                </div>

                <!-- 主图表区域 -->
                <div class="absolute" style="top: ${topPadding}px; left: ${leftPadding}px; right: ${rightPadding}px; height: ${chartHeight}px;">
                    <svg width="100%" height="100%" viewBox="0 0 ${chartWidth} ${chartHeight}" style="background: #f8fafc;">
                        <!-- 定义渐变 -->
                        <defs>
                            <linearGradient id="eqGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
                                <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.1" />
                            </linearGradient>

                            <!-- 网格线图案 -->
                            <pattern id="gridPattern" width="50" height="30" patternUnits="userSpaceOnUse">
                                <path d="M 50 0 L 0 0 0 30" fill="none" stroke="#e2e8f0" stroke-width="1"/>
                            </pattern>
                        </defs>

                        <!-- 网格背景（只在数据区域） -->
                        <rect x="${drawingStartX}" y="0" width="${drawingWidth}" height="100%" fill="url(#gridPattern)" />

                        <!-- 水平参考线（只在数据区域） -->
                        <line x1="${drawingStartX}" y1="45" x2="${drawingStartX + drawingWidth}" y2="45" stroke="#cbd5e1" stroke-width="1" opacity="0.6"/>
                        <line x1="${drawingStartX}" y1="90" x2="${drawingStartX + drawingWidth}" y2="90" stroke="#64748b" stroke-width="1.5"/>
                        <line x1="${drawingStartX}" y1="135" x2="${drawingStartX + drawingWidth}" y2="135" stroke="#cbd5e1" stroke-width="1" opacity="0.6"/>

                        <!-- EQ曲线填充 -->
                        <path d="${this.generateEQPath(values, drawingWidth, chartHeight, drawingStartX)}" fill="url(#eqGradient)" stroke="none"/>

                        <!-- EQ曲线描边 -->
                        <path d="${this.generateSmoothEQCurve(values, drawingWidth, chartHeight, drawingStartX)}" fill="none" stroke="#3b82f6" stroke-width="2"/>

                        <!-- 控制点 -->
                        ${values.map((value, index) => {
                            const x = drawingStartX + (index * (drawingWidth / (values.length - 1)));
                            const y = (chartHeight / 2) - (value * 15); // 每dB对应15像素
                            return `<circle cx="${x}" cy="${y}" r="${controlPointRadius}" fill="#ffffff" stroke="#3b82f6" stroke-width="2"/>`;
                        }).join('')}
                    </svg>
                </div>

                <!-- 左侧dB标签 -->
                <div class="absolute left-0 flex flex-col justify-between text-xs text-gray-500" style="top: ${topPadding}px; height: ${chartHeight}px; padding: 10px 0;">
                    <span>+6 dB</span>
                    <span>0 dB</span>
                    <span>-6 dB</span>
                </div>

                <!-- 底部频段标签 -->
                <div class="absolute flex text-sm text-gray-600" style="top: ${topPadding + chartHeight + 15}px; left: ${leftPadding}px; right: ${rightPadding}px;">
                    ${freqs.map((freq, index) => {
                        const controlPointX = drawingStartX + (index * (drawingWidth / (values.length - 1)));
                        const leftPos = (controlPointX / chartWidth) * 100;
                        return `<span style="position: absolute; left: ${leftPos}%; transform: translateX(-50%);">${freq}</span>`;
                    }).join('')}
                </div>

                <!-- 最底部低频/高频标签 -->
                <div class="absolute bottom-0 flex justify-between text-xs text-gray-400" style="left: ${leftPadding}px; right: ${rightPadding}px;">
                    <span>低频</span>
                    <span>高频</span>
                </div>
            </div>
        `;

        return svg;
    }

    // 生成EQ填充区域路径
    generateEQPath(values, width = 280, height = 180, startX = 10) {
        const centerY = height / 2; // 中心线位置
        const pixelsPerDB = 15; // 每dB对应15像素

        // 获取平滑曲线路径
        const smoothCurve = this.generateSmoothEQCurve(values, width, height, startX);

        // 创建填充区域路径（从底部开始，绘制曲线，然后回到底部闭合）
        const firstX = startX;
        const lastX = startX + width;

        let fillPath = `M ${firstX} ${height} L ${firstX} ${centerY - (values[0] * pixelsPerDB)}`;
        fillPath += smoothCurve.substring(1); // 移除开头的M，添加曲线部分
        fillPath += ` L ${lastX} ${height} Z`; // 闭合到底部

        return fillPath;
    }

    // 生成平滑的EQ曲线（基于所有控制点的样条曲线）
    generateSmoothEQCurve(values, width = 280, height = 180, startX = 10) {
        const centerY = height / 2;
        const pixelsPerDB = 15;

        // 计算控制点坐标
        const points = values.map((value, index) => ({
            x: startX + (index * (width / (values.length - 1))),
            y: centerY - (value * pixelsPerDB)
        }));

        if (points.length < 2) return '';

        // 使用Catmull-Rom样条曲线生成平滑路径
        let path = `M ${points[0].x} ${points[0].y}`;

        for (let i = 0; i < points.length - 1; i++) {
            const p0 = points[Math.max(0, i - 1)];
            const p1 = points[i];
            const p2 = points[i + 1];
            const p3 = points[Math.min(points.length - 1, i + 2)];

            // 计算控制点
            const cp1x = p1.x + (p2.x - p0.x) / 6;
            const cp1y = p1.y + (p2.y - p0.y) / 6;
            const cp2x = p2.x - (p3.x - p1.x) / 6;
            const cp2y = p2.y - (p3.y - p1.y) / 6;

            path += ` C ${cp1x} ${cp1y} ${cp2x} ${cp2y} ${p2.x} ${p2.y}`;
        }

        return path;
    }


    // 点赞EQ
    async likeEQ(eqId) {
        try {
            const response = await fetch(`/api/eq/${eqId}/like`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ userId: this.userID })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // 重新显示详情以更新数据
                this.showEQDetails(eqId);
                // 重新加载列表
                this.loadEQs();
            } else {
                alert(result.error === 'Already liked' ? '您已经点赞过了' : '点赞失败');
            }
        } catch (error) {
            console.error('点赞失败:', error);
            alert('点赞失败，请稍后重试');
        }
    }
    
    // 一键导入欢律
    importToHuanlu(eqId) {
        const eq = this.eqs.find(e => e.id === eqId);
        if (eq) {
            // 按照频段顺序获取数值：62, 250, 1k, 4k, 8k, 16k
            const frequencies = ['62', '250', '1k', '4k', '8k', '16k'];
            const values = frequencies.map(freq => {
                const value = eq.frequencies[freq] || 0;
                return value > 0 ? `+${value}` : `${value}`;
            });

            // 输出到控制台供外部程序检测
            const importCommand = `[Seeq]{${values.join(',')}}`;
            console.log(importCommand);
        }
    }
    
    // 上传EQ图片并分析
    async uploadEQImage() {
        const form = document.getElementById('uploadForm');
        const formData = new FormData(form);
        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');
        const loadingSpinner = document.getElementById('loadingSpinner');

        // 显示加载状态
        submitBtn.disabled = true;
        submitText.classList.add('hidden');
        loadingSpinner.classList.remove('hidden');

        try {
            const response = await fetch('/api/eq/upload-image', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                // 显示OCR分析结果
                console.log('收到的分析结果:', result.data);
                this.showAnalysisResult(result.data);

                // 延迟显示成功消息
                setTimeout(() => {
                    alert('EQ设置上传成功！OCR已成功分析您的截图。\n\n请查看调试信息，然后告诉开发者如何提取EQ参数。');
                    this.hideUploadModal();
                    this.loadEQs(); // 重新加载列表
                    this.resetUploadForm();
                }, 2000); // 增加延迟时间，让用户有时间查看调试信息
            } else {
                alert('上传失败: ' + result.error);
                this.resetSubmitButton();
            }
        } catch (error) {
            console.error('上传失败:', error);
            alert('上传失败，请稍后重试');
            this.resetSubmitButton();
        }
    }

    // 显示OCR分析结果
    showAnalysisResult(data) {
        const analysisResult = document.getElementById('aiAnalysisResult');
        const analysisPreview = document.getElementById('analysisPreview');

        const frequencies = data.frequencies || {};
        const ocrText = data.ocrText || '';
        const ocrLines = data.ocrLines || [];
        const debug = data.debug || {};

        const freqs = [
            { key: '62', label: '62Hz' },
            { key: '250', label: '250Hz' },
            { key: '1k', label: '1kHz' },
            { key: '4k', label: '4kHz' },
            { key: '8k', label: '8kHz' },
            { key: '16k', label: '16kHz' }
        ];

        const resultHTML = `
            <div class="text-sm text-green-600 mb-3">✓ OCR分析完成！</div>

            <!-- EQ参数结果 -->
            <div class="mb-4">
                <div class="text-sm font-medium text-gray-700 mb-2">提取的EQ参数：</div>
                <div class="grid grid-cols-3 gap-2 text-sm">
                    ${freqs.map(freq => {
                        const value = frequencies[freq.key] || 0;
                        return `
                            <div class="flex justify-between items-center bg-white rounded-lg px-3 py-2">
                                <span class="text-gray-600">${freq.label}</span>
                                <span class="font-semibold ${value > 0 ? 'text-green-600' : value < 0 ? 'text-red-600' : 'text-gray-600'}">
                                    ${value > 0 ? '+' : ''}${value}dB
                                </span>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>

            <!-- OCR调试信息 -->
            <div class="border-t pt-4">
                <div class="text-sm font-medium text-gray-700 mb-2">OCR调试信息：</div>
                <div class="text-xs text-gray-600 mb-2">
                    原图大小: ${debug.originalImageSize ? Math.round(debug.originalImageSize / 1024) + 'KB' : '未知'} |
                    处理后: ${debug.processedImageSize ? Math.round(debug.processedImageSize / 1024) + 'KB' : '未知'} |
                    识别行数: ${debug.linesCount || ocrLines.length}
                </div>

                <!-- 完整OCR文本 -->
                <details class="mb-3">
                    <summary class="text-sm font-medium text-gray-700 cursor-pointer hover:text-blue-600">
                        完整OCR识别文本 (点击展开)
                    </summary>
                    <div class="mt-2 p-3 bg-gray-100 rounded text-xs font-mono whitespace-pre-wrap max-h-32 overflow-y-auto">
${ocrText}
                    </div>
                </details>

                <!-- 按行分割的文本 -->
                <details>
                    <summary class="text-sm font-medium text-gray-700 cursor-pointer hover:text-blue-600">
                        按行分割的文本 (点击展开)
                    </summary>
                    <div class="mt-2 max-h-40 overflow-y-auto">
                        ${ocrLines.map((line, index) => `
                            <div class="text-xs p-2 border-b border-gray-200 font-mono">
                                <span class="text-gray-500 mr-2">${index + 1}:</span>
                                <span class="text-gray-800">"${line.trim()}"</span>
                            </div>
                        `).join('')}
                    </div>
                </details>
            </div>
        `;

        analysisPreview.innerHTML = resultHTML;
        analysisResult.classList.remove('hidden');

        // 添加动画效果
        gsap.fromTo(analysisResult,
            { opacity: 0, y: 20 },
            { opacity: 1, y: 0, duration: 0.5 }
        );
    }

    // 重置提交按钮
    resetSubmitButton() {
        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');
        const loadingSpinner = document.getElementById('loadingSpinner');

        submitBtn.disabled = false;
        submitText.classList.remove('hidden');
        loadingSpinner.classList.add('hidden');
        submitText.textContent = '上传并分析EQ设置';
    }

    // 重置上传表单
    resetUploadForm() {
        document.getElementById('uploadForm').reset();
        document.getElementById('uploadPlaceholder').classList.remove('hidden');
        document.getElementById('imagePreview').classList.add('hidden');
        document.getElementById('aiAnalysisResult').classList.add('hidden');

        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');
        submitBtn.disabled = true;
        submitText.textContent = '请先上传EQ截图';

        this.resetSubmitButton();
    }
    

    
    // 显示/隐藏模态框
    showEQModal() {
        const modal = document.getElementById('eqModal');
        modal.classList.remove('hidden');
        gsap.fromTo(modal.querySelector('.bg-white'), 
            { scale: 0.8, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.3 }
        );
    }
    
    hideEQModal() {
        const modal = document.getElementById('eqModal');
        gsap.to(modal.querySelector('.bg-white'), {
            scale: 0.8,
            opacity: 0,
            duration: 0.2,
            onComplete: () => modal.classList.add('hidden')
        });
    }
    
    showUploadModal() {
        const modal = document.getElementById('uploadModal');
        modal.classList.remove('hidden');
        gsap.fromTo(modal.querySelector('.bg-white'), 
            { scale: 0.8, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.3 }
        );
    }
    
    hideUploadModal() {
        const modal = document.getElementById('uploadModal');
        gsap.to(modal.querySelector('.bg-white'), {
            scale: 0.8,
            opacity: 0,
            duration: 0.2,
            onComplete: () => modal.classList.add('hidden')
        });
    }
    
    // 显示加载状态
    showLoading() {
        document.getElementById('loading').classList.remove('hidden');
        document.getElementById('eqGrid').classList.add('hidden');
        document.getElementById('emptyState').classList.add('hidden');
    }
    
    // 显示空状态
    showEmptyState() {
        document.getElementById('loading').classList.add('hidden');
        document.getElementById('eqGrid').classList.add('hidden');
        document.getElementById('emptyState').classList.remove('hidden');
    }
    
    // 页面加载动画
    animatePageLoad() {
        gsap.fromTo('nav', 
            { y: -100, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.6 }
        );
        
        gsap.fromTo('main > div:first-child', 
            { y: 50, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.8, delay: 0.2 }
        );
        
        gsap.fromTo('.floating-action', 
            { scale: 0, rotation: 180 },
            { scale: 1, rotation: 0, duration: 0.6, delay: 0.8 }
        );
    }
}

// 初始化应用
const seeqApp = new SeeqApp();
