<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seeq - OPPO耳机EQ分享社区</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .floating-action {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 50;
        }

        /* EQ垂直滑块样式 */
        .eq-slider {
            -webkit-appearance: slider-vertical;
            width: 6px;
            height: 120px;
            background: #e5e7eb;
            outline: none;
            border-radius: 3px;
            writing-mode: bt-lr; /* IE */
            -webkit-appearance: slider-vertical; /* WebKit */
            transform: rotate(180deg); /* 翻转滑块方向 */
        }

        .eq-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            border: 2px solid #ffffff;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            transform: rotate(180deg); /* 翻转拇指方向 */
        }

        .eq-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            border: 2px solid #ffffff;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            border: none;
        }

        /* Firefox特殊处理 */
        .eq-slider::-moz-range-track {
            background: #e5e7eb;
            height: 6px;
            border-radius: 3px;
        }

        /* EQ图表样式 */
        .eq-chart {
            background: linear-gradient(to bottom,
                rgba(16, 185, 129, 0.1) 0%,
                rgba(16, 185, 129, 0.05) 50%,
                rgba(239, 68, 68, 0.05) 50%,
                rgba(239, 68, 68, 0.1) 100%);
        }

        .eq-grid-line {
            stroke: #d1d5db;
            stroke-width: 1;
            opacity: 0.5;
        }

        .eq-curve {
            fill: rgba(59, 130, 246, 0.2);
            stroke: #3b82f6;
            stroke-width: 3;
        }

        .eq-point {
            fill: #3b82f6;
            stroke: #ffffff;
            stroke-width: 2;
            cursor: pointer;
        }

        .eq-point:hover {
            fill: #1d4ed8;
            transform: scale(1.2);
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .floating-action {
                bottom: 1rem;
                right: 1rem;
                width: 56px;
                height: 56px;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="glass-effect sticky top-0 z-40 px-4 py-3">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 gradient-bg rounded-xl flex items-center justify-center">
                    <span class="text-white font-bold text-lg">S</span>
                </div>
                <h1 class="text-2xl font-bold text-gray-800">Seeq</h1>
            </div>
            
            <div class="flex items-center space-x-4">
                <!-- 分类筛选 -->
                <select id="categoryFilter" class="px-4 py-2 rounded-xl bg-white border border-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="all">所有耳机</option>
                    <option value="OPPO ENCO Free4">OPPO ENCO Free4</option>
                    <option value="OPPO ENCO X3">OPPO ENCO X3</option>
                </select>
                
                <!-- 排序选择 -->
                <select id="sortFilter" class="px-4 py-2 rounded-xl bg-white border border-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="views">浏览量</option>
                    <option value="likes">点赞量</option>
                    <option value="happiness">欢率</option>
                    <option value="newest">最新</option>
                    <option value="oldest">最旧</option>
                </select>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-7xl mx-auto px-4 py-8">
        <!-- 标题区域 -->
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-gray-800 mb-4">发现完美的EQ设置</h2>
            <p class="text-gray-600 text-lg">与OPPO耳机用户分享和探索最佳的音频体验</p>
        </div>

        <!-- 加载状态 -->
        <div id="loading" class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">加载中...</p>
        </div>

        <!-- EQ卡片网格 -->
        <div id="eqGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 hidden">
            <!-- EQ卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 空状态 -->
        <div id="emptyState" class="text-center py-12 hidden">
            <div class="text-gray-400 text-6xl mb-4">🎵</div>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">暂无EQ设置</h3>
            <p class="text-gray-500">成为第一个分享EQ设置的用户吧！</p>
        </div>
    </main>

    <!-- 浮动上传按钮 -->
    <button id="uploadBtn" class="floating-action w-16 h-16 gradient-bg rounded-full shadow-lg flex items-center justify-center text-white hover:shadow-xl transition-all duration-300">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
    </button>

    <!-- EQ详情模态框 -->
    <div id="eqModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div id="modalContent">
                <!-- 模态框内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 上传EQ模态框 -->
    <div id="uploadModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-gray-800">上传EQ设置</h3>
                    <button id="closeUploadModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <form id="uploadForm" class="space-y-6" enctype="multipart/form-data">
                    <!-- 图片上传区域 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-4">上传欢律APP的EQ截图</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors" id="imageUploadArea">
                            <input type="file" id="eqImage" name="eqImage" accept="image/*" required class="hidden">
                            <div id="uploadPlaceholder">
                                <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <p class="text-gray-600 mb-2">点击上传或拖拽图片到此处</p>
                                <p class="text-sm text-gray-500">支持 JPG、PNG 格式</p>
                            </div>
                            <div id="imagePreview" class="hidden">
                                <img id="previewImg" class="max-h-48 mx-auto rounded-lg mb-4">
                                <p class="text-sm text-green-600">✓ 图片已选择，AI将自动分析EQ参数</p>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">EQ名称</label>
                        <input type="text" id="eqName" name="name" required class="w-full px-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="给你的EQ起个名字">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                        <textarea id="eqDescription" name="description" required class="w-full px-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 h-20" placeholder="描述这个EQ的特点和适用场景"></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">耳机型号</label>
                        <select id="eqCategory" name="category" required class="w-full px-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">选择耳机型号</option>
                            <option value="OPPO ENCO Free4">OPPO ENCO Free4</option>
                            <option value="OPPO ENCO X3">OPPO ENCO X3</option>
                        </select>
                    </div>



                    <!-- OCR分析结果预览 -->
                    <div id="aiAnalysisResult" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-4">OCR分析结果</label>
                        <div class="bg-blue-50 rounded-xl p-4">
                            <div id="analysisPreview">
                                <!-- AI分析结果将在这里显示 -->
                            </div>
                        </div>
                    </div>

                    <button type="submit" id="submitBtn" class="w-full gradient-bg text-white py-3 rounded-xl font-medium hover:opacity-90 transition-opacity" disabled>
                        <span id="submitText">请先上传EQ截图</span>
                        <span id="loadingSpinner" class="hidden">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            OCR分析中...
                        </span>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
